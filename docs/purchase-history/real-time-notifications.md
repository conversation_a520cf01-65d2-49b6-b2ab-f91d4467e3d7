# Sistema de Notificações em Tempo Real para Pagamentos

Este documento descreve a implementação do sistema de notificações em tempo real que permite fechar automaticamente o modal do QR Code quando um pagamento é aprovado.

## Visão Geral

O sistema utiliza **Server-Sent Events (SSE)** para estabelecer uma conexão em tempo real entre o servidor e o cliente, permitindo que o webhook notifique instantaneamente o usuário sobre mudanças no status do pagamento.

## Arquitetura

```
Webhook (Pagar.me/Mercado Pago)
    ↓
Processamento do Pagamento
    ↓
Notificação SSE para o Usuário
    ↓
Frontend Recebe Evento
    ↓
Modal do QR Code Fechado Automaticamente
```

## Componentes Implementados

### 1. Endpoint SSE (`/api/payment-events`)

**Arquivo**: `src/app/api/payment-events/route.ts`

**Funcionalidades**:
- Mantém conexões ativas por usuário
- Envia eventos de pagamento em tempo real
- Gerencia cleanup automático de conexões

**Eventos Suportados**:
- `connected`: Confirmação de conexão estabelecida
- `payment_approved`: Pagamento aprovado com sucesso
- `payment_failed`: Pagamento falhou ou foi rejeitado

### 2. Hook React (`usePaymentEvents`)

**Arquivo**: `src/hooks/usePaymentEvents.ts`

**Funcionalidades**:
- Estabelece conexão SSE automaticamente
- Reconexão automática com backoff exponencial
- Callbacks para diferentes tipos de eventos
- Cleanup automático ao desmontar componente

**Opções de Configuração**:
```typescript
interface UsePaymentEventsOptions {
  onPaymentApproved?: (event: PaymentEvent) => void;
  onPaymentFailed?: (event: PaymentEvent) => void;
  onConnected?: () => void;
  enabled?: boolean;
}
```

### 3. Integração com Webhooks

**Modificações nos Webhooks**:
- Pagar.me: `src/app/api/webhooks/pagarme/route.ts`
- Mercado Pago: `src/app/api/webhooks/mercadopago/route.ts`

**Função de Notificação**:
```typescript
notifyPaymentStatus(userId: string, {
  type: 'payment_approved',
  orderCode: string,
  amount: number,
  coins: number,
});
```

### 4. Integração na Página de Compras

**Arquivo**: `src/app/buy-coins/page.tsx`

**Implementação**:
- Hook ativado apenas quando modal PIX está aberto
- Fecha modal automaticamente ao receber `payment_approved`
- Exibe toast de sucesso com informações da compra
- Atualização automática do saldo via listeners do Firestore

## Fluxo de Funcionamento

### 1. Abertura do Modal PIX
1. Usuário seleciona pacote e clica em "Comprar"
2. Modal PIX é aberto com QR Code
3. Hook `usePaymentEvents` é ativado (`enabled: !!pixData`)
4. Conexão SSE é estabelecida com o servidor

### 2. Processamento do Pagamento
1. Usuário paga o PIX
2. Gateway de pagamento envia webhook
3. Webhook processa pagamento e adiciona moedas
4. Webhook chama `notifyPaymentStatus()` para o usuário

### 3. Notificação em Tempo Real
1. Servidor envia evento SSE para o cliente
2. Hook recebe evento `payment_approved`
3. Modal PIX é fechado automaticamente
4. Toast de sucesso é exibido
5. Saldo de moedas é atualizado automaticamente

## Características Técnicas

### Server-Sent Events (SSE)
- **Protocolo**: HTTP/1.1 com `text/event-stream`
- **Formato**: `data: {JSON}\n\n`
- **Headers**: Cache-Control, Connection keep-alive
- **Timeout**: Gerenciado pelo navegador (padrão ~45s)

### Reconexão Automática
- **Estratégia**: Backoff exponencial
- **Tentativas**: Máximo 5 tentativas
- **Delay**: 1s, 2s, 4s, 8s, 16s (máximo 30s)
- **Reset**: Contador zerado em conexão bem-sucedida

### Gerenciamento de Memória
- **Cleanup**: Conexões removidas automaticamente
- **Listeners**: Event listeners removidos no unmount
- **Timeouts**: Clearance automática de timeouts

## Vantagens da Implementação

### 1. Experiência do Usuário
- ✅ Feedback instantâneo sobre pagamento
- ✅ Modal fechado automaticamente
- ✅ Sem necessidade de refresh manual
- ✅ Notificações visuais claras

### 2. Performance
- ✅ Conexão leve (apenas texto)
- ✅ Sem polling desnecessário
- ✅ Reconexão inteligente
- ✅ Cleanup automático

### 3. Confiabilidade
- ✅ Tolerante a falhas de rede
- ✅ Reconexão automática
- ✅ Fallback para atualização manual
- ✅ Logs detalhados para debugging

## Configuração e Deployment

### Variáveis de Ambiente
Nenhuma variável adicional necessária. Utiliza as mesmas configurações dos webhooks existentes.

### Considerações de Produção

#### 1. Escalabilidade
- **Conexões Simultâneas**: Limitadas pela memória do servidor
- **Recomendação**: Usar Redis para armazenar conexões em ambiente distribuído
- **Alternativa**: WebSockets para maior controle

#### 2. Monitoramento
```typescript
// Métricas importantes
- Número de conexões ativas
- Taxa de reconexão
- Tempo de resposta dos eventos
- Erros de conexão
```

#### 3. Segurança
- **Autenticação**: Verificação de userId obrigatória
- **Rate Limiting**: Implementado nos webhooks
- **CORS**: Configurado para domínio específico

## Testes Recomendados

### 1. Testes Funcionais
- [ ] Conexão SSE estabelecida corretamente
- [ ] Evento `payment_approved` fecha modal
- [ ] Reconexão após perda de conexão
- [ ] Cleanup ao fechar página

### 2. Testes de Performance
- [ ] Múltiplas conexões simultâneas
- [ ] Tempo de resposta dos eventos
- [ ] Uso de memória do servidor
- [ ] Comportamento com rede instável

### 3. Testes de Integração
- [ ] Fluxo completo: compra → pagamento → notificação
- [ ] Webhooks Pagar.me e Mercado Pago
- [ ] Diferentes tipos de pagamento (PIX, cartão)
- [ ] Cenários de erro e recuperação

## Troubleshooting

### Problemas Comuns

#### 1. Conexão SSE não estabelecida
```typescript
// Verificar no console do navegador
console.log('EventSource readyState:', eventSource.readyState);
// 0: CONNECTING, 1: OPEN, 2: CLOSED
```

#### 2. Eventos não recebidos
```typescript
// Verificar logs do servidor
console.log('Active connections:', connections.size);
console.log('Sending event to user:', userId);
```

#### 3. Modal não fecha automaticamente
```typescript
// Verificar se hook está ativado
console.log('Payment events enabled:', !!pixData);
```

## Melhorias Futuras

### 1. Funcionalidades
- [ ] Notificações para outros eventos (expiração PIX)
- [ ] Histórico de notificações
- [ ] Configurações de notificação por usuário
- [ ] Suporte a múltiplas abas/dispositivos

### 2. Tecnologia
- [ ] Migração para WebSockets
- [ ] Integração com Redis para escalabilidade
- [ ] Push notifications para mobile
- [ ] Offline support com service workers

### 3. Analytics
- [ ] Métricas de engajamento
- [ ] Tempo médio até fechamento do modal
- [ ] Taxa de conversão com notificações
- [ ] Análise de comportamento do usuário
