# Especificações Técnicas - Sistema de Histórico de Compras

## Arquitetura Geral

### Stack Tecnológico
- **Frontend**: Next.js 14 com App Router
- **Backend**: Next.js API Routes (Server Actions)
- **Banco de Dados**: Firebase Firestore
- **UI**: shadcn/ui + Tailwind CSS
- **Validação**: TypeScript interfaces
- **Formatação**: date-fns + Intl API

### Fluxo de Dados
```
Webhook (Pagar.me/Mercado Pago) 
    ↓
Validação e Processamento
    ↓
Criação de Purchase (Firestore)
    ↓
Adição de Moedas (Wallet)
    ↓
Interface do Usuário (Histórico)
```

## Estrutura do Banco de Dados

### Coleção: `purchases`

#### Documento Structure
```typescript
{
  id: string; // Auto-generated document ID
  userId: string; // Firebase Auth UID
  orderCode: string; // Código único do pedido
  packageId: string; // ID do pacote de moedas
  packageName: string; // Nome do pacote
  amount: number; // Valor em centavos
  coins: number; // Quantidade de moedas
  status: 'pending' | 'paid' | 'canceled' | 'failed';
  paymentMethod: 'pix' | 'credit_card';
  gateway: 'pagarme' | 'mercadopago';
  gatewayTransactionId: string; // ID da transação no gateway
  gatewayOrderId?: string; // ID do pedido no gateway
  metadata?: { [key: string]: string }; // Dados adicionais
  createdAt: Timestamp; // Data de criação
  paidAt?: Timestamp; // Data de pagamento (se pago)
  updatedAt: Timestamp; // Data de última atualização
}
```

#### Índices Necessários
```javascript
// Índice composto para listagem por usuário
{
  fields: ['userId', 'createdAt'],
  order: ['userId', 'createdAt DESC']
}

// Índice para filtros por status
{
  fields: ['userId', 'status', 'createdAt'],
  order: ['userId', 'status', 'createdAt DESC']
}

// Índice para prevenção de duplicatas
{
  fields: ['gatewayTransactionId', 'gateway'],
  order: ['gatewayTransactionId', 'gateway']
}

// Índice para filtros por gateway
{
  fields: ['userId', 'gateway', 'createdAt'],
  order: ['userId', 'gateway', 'createdAt DESC']
}
```

### Regras de Segurança Firestore
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Purchases collection
    match /purchases/{purchaseId} {
      // Users can only read their own purchases
      allow read: if request.auth != null && 
                     request.auth.uid == resource.data.userId;
      
      // Only server can create/update purchases
      allow write: if false;
    }
  }
}
```

## API Endpoints

### Webhooks

#### POST /api/webhooks/pagarme
**Função**: Processar notificações de pagamento do Pagar.me

**Headers Esperados**:
- `x-hub-signature-256`: Assinatura HMAC SHA256
- `content-type`: application/json

**Payload Exemplo**:
```json
{
  "type": "charge.paid",
  "data": {
    "id": "ch_abc123",
    "status": "paid",
    "amount": 1000,
    "payment_method": "pix",
    "order": {
      "id": "or_def456",
      "code": "PM_basic_abc123",
      "metadata": {
        "userId": "user123",
        "packageId": "basic",
        "app": "PosterMuse",
        "version": "1.0.0"
      }
    }
  }
}
```

#### POST /api/webhooks/mercadopago
**Função**: Processar notificações de pagamento do Mercado Pago

**Headers Esperados**:
- `x-signature`: Assinatura do Mercado Pago
- `x-request-id`: ID da requisição
- `content-type`: application/json

**Payload Exemplo**:
```json
{
  "type": "payment",
  "data": {
    "id": 123456789
  }
}
```

### Server Actions

#### getUserPurchases
```typescript
async function getUserPurchases(
  userId: string,
  filters: PurchaseFilters = {},
  pageSize: number = 20,
  lastDoc?: QueryDocumentSnapshot<DocumentData>
): Promise<PaginatedPurchases>
```

#### getUserPurchaseStats
```typescript
async function getUserPurchaseStats(userId: string): Promise<{
  totalPurchases: number;
  totalSpent: number;
  totalCoins: number;
  successfulPurchases: number;
}>
```

## Componentes React

### PurchaseCard
**Props**:
```typescript
interface PurchaseCardProps {
  purchase: Purchase;
  showDetails?: boolean;
  onViewDetails?: (purchase: Purchase) => void;
}
```

**Funcionalidades**:
- Exibição formatada de dados da compra
- Badge de status com cores apropriadas
- Informações de data e valor
- Botão opcional para detalhes

### PurchaseStats
**Props**:
```typescript
interface PurchaseStatsProps {
  stats: PurchaseStatsData;
  loading?: boolean;
}
```

**Métricas Exibidas**:
- Total de compras realizadas
- Compras aprovadas com taxa de sucesso
- Valor total gasto
- Total de moedas adquiridas

### Página purchase-history
**Estado Gerenciado**:
```typescript
const [purchases, setPurchases] = useState<Purchase[]>([]);
const [stats, setStats] = useState<PurchaseStatsData | null>(null);
const [loading, setLoading] = useState(true);
const [filters, setFilters] = useState<PurchaseFilters>({});
const [hasMore, setHasMore] = useState(false);
```

## Tratamento de Erros

### Webhooks
- Validação de assinatura
- Rate limiting por IP
- Prevenção de duplicatas
- Logs detalhados de erro
- Resposta HTTP apropriada

### Interface do Usuário
- Estados de loading
- Mensagens de erro amigáveis
- Fallbacks para dados não disponíveis
- Retry automático em falhas

### Firestore
- Tratamento de permissões
- Timeout de consultas
- Paginação segura
- Validação de dados

## Performance

### Otimizações Implementadas
1. **Paginação**: Limite de 20 itens por página
2. **Índices**: Consultas otimizadas no Firestore
3. **Lazy Loading**: Carregamento sob demanda
4. **Memoização**: React.useCallback para funções
5. **Skeleton Loading**: Feedback visual durante carregamento

### Métricas de Performance
- Tempo de carregamento inicial: < 2s
- Tempo de paginação: < 500ms
- Tamanho do bundle: Componentes tree-shaken
- Consultas Firestore: Máximo 1 read por item

## Segurança

### Autenticação
- Firebase Auth obrigatório
- Verificação de UID em todas as operações
- Isolamento de dados por usuário

### Validação de Webhooks
- Assinatura HMAC verificada
- Rate limiting implementado
- Logs de tentativas suspeitas

### Dados Sensíveis
- Valores monetários em centavos
- Metadados estruturados
- Sem dados de cartão armazenados

## Monitoramento e Logs

### Eventos Logados
```typescript
// Webhook processing
console.log('Webhook received:', { type, gateway, transactionId });
console.log('Purchase created:', { userId, amount, coins });
console.error('Webhook error:', { error, payload });

// User interactions
console.log('Purchase history loaded:', { userId, count, filters });
console.error('Purchase fetch error:', { userId, error });
```

### Métricas Importantes
- Taxa de sucesso dos webhooks (> 99%)
- Tempo de resposta da página (< 2s)
- Erros de duplicação (< 0.1%)
- Performance das consultas (< 500ms)

## Deployment

### Variáveis de Ambiente
```env
# Pagar.me
PAGARME_SECRET_KEY=sk_test_...
PAGARME_WEBHOOK_SECRET=whsec_...

# Mercado Pago
MERCADOPAGO_ACCESS_TOKEN=APP_USR-...
MERCADOPAGO_WEBHOOK_SECRET=...

# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=...
NEXT_PUBLIC_FIREBASE_PROJECT_ID=...
```

### Build Requirements
- Node.js 18+
- Next.js 14+
- Firebase Admin SDK configurado
- Webhooks endpoints públicos

## Testes

### Testes Unitários Recomendados
```typescript
// purchaseService.ts
describe('createPurchase', () => {
  it('should create purchase with correct data');
  it('should handle Firestore errors');
});

// purchase-utils.ts
describe('formatCurrency', () => {
  it('should format cents to BRL currency');
});

// PurchaseCard component
describe('PurchaseCard', () => {
  it('should render purchase data correctly');
  it('should show correct status badge');
});
```

### Testes de Integração
- Webhook end-to-end com payloads reais
- Fluxo completo de compra → webhook → histórico
- Performance com grandes volumes de dados

### Testes Manuais
- Interface responsiva em diferentes dispositivos
- Filtros e paginação funcionando
- Estados de erro e loading
- Acessibilidade (screen readers, keyboard navigation)
