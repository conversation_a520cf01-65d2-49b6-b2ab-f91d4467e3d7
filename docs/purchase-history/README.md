# Sistema de Histórico de Compras

Este documento descreve a implementação do sistema de histórico de compras, que permite salvar e visualizar informações detalhadas sobre transações de pagamento.

## Funcionalidades Implementadas

### 1. Salvamento de Transações

- **Modelo de Dados**: Criado interface `Purchase` com todas as informações necessárias
- **Serviço de Compras**: `purchaseService.ts` com funções para criar, buscar e gerenciar transações
- **Integração com Webhooks**: Modificados webhooks do Pagar.me e Mercado Pago para salvar transações automaticamente

### 2. Página de Histórico de Compras

- **Rota**: `/purchase-history`
- **Funcionalidades**:
  - Lista paginada de todas as compras do usuário
  - Filtros por status, gateway e método de pagamento
  - Estatísticas resumidas (total de compras, valor gasto, moedas adquiridas)
  - Interface responsiva e acessível

### 3. Componentes Reutilizáveis

- **PurchaseCard**: Card individual para exibir detalhes de uma compra
- **PurchaseStats**: Componente de estatísticas com métricas importantes
- **Utilitários**: Funções para formatação de valores, datas e status

## Estrutura de Arquivos

```
src/
├── services/
│   └── purchaseService.ts          # Serviço principal para gerenciar compras
├── app/
│   ├── purchase-history/
│   │   └── page.tsx               # Página do histórico de compras
│   └── api/webhooks/
│       ├── pagarme/route.ts       # Webhook modificado para salvar transações
│       └── mercadopago/route.ts   # Webhook modificado para salvar transações
├── components/
│   ├── purchase-card.tsx          # Componente de card de compra
│   ├── purchase-stats.tsx         # Componente de estatísticas
│   └── user-nav.tsx              # Navegação atualizada com link para histórico
└── lib/
    └── purchase-utils.ts          # Utilitários para formatação e cálculos
```

## Modelo de Dados

### Interface Purchase

```typescript
interface Purchase {
  id: string;
  userId: string;
  orderCode: string;
  packageId: string;
  packageName: string;
  amount: number; // Valor em centavos
  coins: number; // Quantidade de moedas
  status: "pending" | "paid" | "canceled" | "failed";
  paymentMethod: "pix" | "credit_card";
  gateway: "pagarme" | "mercadopago";
  gatewayTransactionId: string;
  gatewayOrderId?: string;
  metadata?: { [key: string]: string };
  createdAt: Timestamp;
  paidAt?: Timestamp;
  updatedAt: Timestamp;
}
```

### Coleção Firebase

- **Nome**: `purchases`
- **Índices Recomendados**:
  - `userId` + `createdAt` (desc)
  - `userId` + `status` + `createdAt` (desc)
  - `gatewayTransactionId` + `gateway`

## Funcionalidades do Serviço

### purchaseService.ts

#### `createPurchase(purchaseData: PurchaseCreateData)`

- Cria uma nova transação no Firestore
- Adiciona timestamps automaticamente
- Retorna o objeto Purchase criado

#### `getUserPurchases(userId, filters?, pageSize?, lastDoc?)`

- Busca compras do usuário com paginação
- Suporte a filtros por status, gateway, método de pagamento
- Retorna lista paginada com indicador de mais resultados

#### `getPurchaseByTransactionId(gatewayTransactionId, gateway)`

- Busca compra por ID da transação do gateway
- Usado para evitar duplicatas nos webhooks
- Retorna Purchase ou null

#### `getUserPurchaseStats(userId)`

- Calcula estatísticas do usuário
- Retorna total de compras, valor gasto, moedas adquiridas, etc.

## Integração com Webhooks

### Modificações Implementadas

#### Webhook Pagar.me

**Eventos Suportados:**

- `charge.created`: Cria transação com status `pending`
- `charge.pending`: Atualiza/confirma transação pendente
- `charge.paid`: Atualiza transação para `paid` e adiciona moedas

**Fluxo de Processamento:**

1. **Created/Pending**: Cria registro com QR Code, URL e data de expiração
2. **Paid**: Atualiza status existente ou cria novo (fallback)
3. **Idempotência**: Previne duplicatas por `gatewayTransactionId`

**Dados Capturados:**

- QR Code PIX (`qr_code`)
- URL do QR Code (`qr_code_url`)
- Data de expiração (`expires_at`)
- Metadados completos do pedido

#### Webhook Mercado Pago

- Mesma lógica do Pagar.me
- Converte valores para centavos
- Mapeia tipos de pagamento corretamente
- **Nota**: Aguardando payloads dos eventos `created` e `pending`

### Prevenção de Duplicatas

- Verificação por `gatewayTransactionId` + `gateway`
- Sistema legado mantido como backup (`processed_payments`)
- Logs detalhados para debugging

## Interface do Usuário

### Página de Histórico (/purchase-history)

#### Funcionalidades

- **Estatísticas**: Cards com métricas importantes
- **Filtros**: Dropdown para status, gateway e método de pagamento
- **Lista de Compras**: Cards com detalhes de cada transação
- **Paginação**: Carregamento incremental com botão "Carregar Mais"
- **Estados**: Loading, erro, lista vazia

#### Informações Exibidas por Compra

- Nome do pacote e código do pedido
- Status com badge colorido
- Quantidade de moedas e valor pago
- Datas de criação e pagamento
- Gateway e método de pagamento

### Navegação

- Link adicionado no menu do usuário (UserNav)
- Acessível através do dropdown do avatar

## Componentes Reutilizáveis

### PurchaseCard

- Exibe informações de uma compra
- Suporte a skeleton loading
- Props para customização de exibição

### PurchaseStats

- Métricas em cards organizados
- Versão compacta disponível
- Cálculos automáticos de taxa de sucesso

### Utilitários (purchase-utils.ts)

- Formatação de moeda brasileira
- Formatação de datas em português
- Labels e cores para status
- Funções de cálculo e agrupamento

## Considerações de Performance

### Otimizações Implementadas

- Paginação com limite de 20 itens por página
- Índices otimizados no Firestore
- Componentes com loading states
- Prevenção de chamadas duplicadas

### Recomendações Futuras

- Cache de estatísticas para usuários com muitas compras
- Implementar filtros por data
- Adicionar exportação de dados
- Implementar busca por texto

## Segurança

### Medidas Implementadas

- Verificação de autenticação em todas as operações
- Filtros por userId para isolamento de dados
- Validação de entrada nos webhooks
- Logs de auditoria para transações

## Testes Recomendados

### Cenários de Teste

1. **Webhook Processing**:

   - Pagamento aprovado no Pagar.me
   - Pagamento aprovado no Mercado Pago
   - Tentativa de processamento duplicado
   - Webhook com dados inválidos

2. **Interface do Usuário**:

   - Carregamento da página sem compras
   - Paginação com muitas compras
   - Filtros funcionando corretamente
   - Responsividade em diferentes telas

3. **Serviços**:
   - Criação de compra
   - Busca com filtros
   - Cálculo de estatísticas
   - Tratamento de erros

## Monitoramento

### Métricas Importantes

- Taxa de sucesso dos webhooks
- Tempo de resposta da página de histórico
- Erros de duplicação de transações
- Performance das consultas no Firestore

### Logs Relevantes

- Processamento de webhooks
- Criação de transações
- Erros de validação
- Consultas lentas no Firestore
