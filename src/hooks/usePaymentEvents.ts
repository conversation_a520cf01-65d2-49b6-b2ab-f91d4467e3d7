'use client';

import { useEffect, useRef, useCallback } from 'react';
import { useAuth } from './useAuth';

export interface PaymentEvent {
  type: 'payment_approved' | 'payment_failed' | 'connected';
  orderCode?: string;
  amount?: number;
  coins?: number;
}

interface UsePaymentEventsOptions {
  onPaymentApproved?: (event: PaymentEvent) => void;
  onPaymentFailed?: (event: PaymentEvent) => void;
  onConnected?: () => void;
  enabled?: boolean;
}

export function usePaymentEvents({
  onPaymentApproved,
  onPaymentFailed,
  onConnected,
  enabled = true,
}: UsePaymentEventsOptions = {}) {
  const { user } = useAuth();
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  const connect = useCallback(() => {
    if (!user?.uid || !enabled) return;

    cleanup();

    try {
      const eventSource = new EventSource(`/api/payment-events?userId=${user.uid}`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('Payment events connection opened');
        reconnectAttempts.current = 0;
      };

      eventSource.onmessage = (event) => {
        try {
          const data: PaymentEvent = JSON.parse(event.data);
          
          switch (data.type) {
            case 'connected':
              onConnected?.();
              break;
            case 'payment_approved':
              onPaymentApproved?.(data);
              break;
            case 'payment_failed':
              onPaymentFailed?.(data);
              break;
          }
        } catch (error) {
          console.error('Error parsing payment event:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Payment events connection error:', error);
        eventSource.close();

        // Implement exponential backoff for reconnection
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectAttempts.current++;
          
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Attempting to reconnect payment events (attempt ${reconnectAttempts.current})`);
            connect();
          }, delay);
        } else {
          console.error('Max reconnection attempts reached for payment events');
        }
      };
    } catch (error) {
      console.error('Error creating payment events connection:', error);
    }
  }, [user?.uid, enabled, onPaymentApproved, onPaymentFailed, onConnected, cleanup]);

  useEffect(() => {
    if (enabled && user?.uid) {
      connect();
    } else {
      cleanup();
    }

    return cleanup;
  }, [enabled, user?.uid, connect, cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  const disconnect = useCallback(() => {
    cleanup();
  }, [cleanup]);

  const reconnect = useCallback(() => {
    reconnectAttempts.current = 0;
    connect();
  }, [connect]);

  return {
    disconnect,
    reconnect,
    isConnected: eventSourceRef.current?.readyState === EventSource.OPEN,
  };
}
