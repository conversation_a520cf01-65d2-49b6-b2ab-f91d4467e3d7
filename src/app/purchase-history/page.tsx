'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { getUserPurchases, getUserPurchaseStats, type Purchase, type PurchaseFilters } from '@/services/purchaseService';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PurchaseCard, PurchaseCardSkeleton } from '@/components/purchase-card';
import { PurchaseStats, type PurchaseStatsData } from '@/components/purchase-stats';
import {
  ShoppingBag,
  Filter,
  RefreshCw
} from 'lucide-react';



function PurchaseHistoryLoading() {
  return (
    <div className="container mx-auto max-w-6xl px-4 py-12">
      <div className="mb-8">
        <div className="h-10 w-64 mb-2 bg-muted rounded animate-pulse" />
        <div className="h-6 w-96 bg-muted rounded animate-pulse" />
      </div>

      <PurchaseStats stats={{ totalPurchases: 0, totalSpent: 0, totalCoins: 0, successfulPurchases: 0 }} loading={true} />

      <div className="space-y-4">
        <PurchaseCardSkeleton count={5} />
      </div>
    </div>
  );
}

export default function PurchaseHistoryPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();

  const [purchases, setPurchases] = React.useState<Purchase[]>([]);
  const [stats, setStats] = React.useState<PurchaseStatsData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [filters, setFilters] = React.useState<PurchaseFilters>({});
  const [hasMore, setHasMore] = React.useState(false);
  const [loadingMore, setLoadingMore] = React.useState(false);

  const loadPurchases = React.useCallback(async (reset = false) => {
    if (!user) return;

    try {
      if (reset) {
        setLoading(true);
        setError(null);
      }

      const result = await getUserPurchases(user.uid, filters, 20);

      if (reset) {
        setPurchases(result.purchases);
        // Load stats only on initial load
        const statsResult = await getUserPurchaseStats(user.uid);
        setStats(statsResult);
      } else {
        setPurchases(prev => [...prev, ...result.purchases]);
      }

      setHasMore(result.hasMore);
    } catch (err) {
      console.error('Error loading purchases:', err);
      setError('Erro ao carregar histórico de compras. Tente novamente.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [user, filters]);

  const loadMore = async () => {
    if (!hasMore || loadingMore) return;
    setLoadingMore(true);
    await loadPurchases(false);
  };

  React.useEffect(() => {
    if (user) {
      loadPurchases(true);
    }
  }, [loadPurchases, user]);

  if (authLoading || loading) {
    return <PurchaseHistoryLoading />;
  }

  if (!user) {
    router.replace('/login');
    return null;
  }

  return (
    <div className="container mx-auto max-w-6xl px-4 py-12">
      <header className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="font-headline text-4xl font-bold tracking-tighter">
              Histórico de Compras
            </h1>
            <p className="text-muted-foreground mt-2 text-lg">
              Acompanhe todas as suas transações e compras de moedas.
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadPurchases(true)}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </header>

      {stats && <PurchaseStats stats={stats} />}

      <div className="flex items-center gap-4 mb-6">
        <Filter className="h-4 w-4 text-muted-foreground" />
        <Select
          value={filters.status || 'all'}
          onValueChange={(value) =>
            setFilters(prev => ({ ...prev, status: value === 'all' ? undefined : value as any }))
          }
        >
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos</SelectItem>
            <SelectItem value="paid">Pago</SelectItem>
            <SelectItem value="pending">Pendente</SelectItem>
            <SelectItem value="canceled">Cancelado</SelectItem>
            <SelectItem value="failed">Falhou</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filters.gateway || 'all'}
          onValueChange={(value) =>
            setFilters(prev => ({ ...prev, gateway: value === 'all' ? undefined : value as any }))
          }
        >
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Gateway" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos</SelectItem>
            <SelectItem value="pagarme">Pagar.me</SelectItem>
            <SelectItem value="mercadopago">Mercado Pago</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {purchases.length === 0 && !loading ? (
        <Card className="text-center py-12">
          <CardContent>
            <ShoppingBag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nenhuma compra encontrada</h3>
            <p className="text-muted-foreground mb-4">
              Você ainda não fez nenhuma compra. Que tal adquirir algumas moedas?
            </p>
            <Button asChild>
              <a href="/buy-coins">Comprar Moedas</a>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {purchases.map((purchase) => (
            <PurchaseCard key={purchase.id} purchase={purchase} />
          ))}

          {hasMore && (
            <div className="text-center pt-4">
              <Button
                variant="outline"
                onClick={loadMore}
                disabled={loadingMore}
              >
                {loadingMore ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Carregando...
                  </>
                ) : (
                  'Carregar Mais'
                )}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
