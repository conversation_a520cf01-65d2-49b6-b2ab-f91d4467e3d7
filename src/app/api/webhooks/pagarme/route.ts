// src/app/api/webhooks/pagarme/route.ts
import { NextResponse } from 'next/server';
import { addCoins } from '@/services/walletService';
import { packages } from '@/services/packages';
import type { PagarmeCharge } from '@/services/pagarmeInterfaces';
import { firestore } from '@/lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { createPurchase, getPurchaseByTransactionId } from '@/services/purchaseService';
import crypto from 'crypto';

const PAGARME_WEBHOOK_SECRET = process.env.PAGARME_WEBHOOK_SECRET;

// Rate limiting - simple in-memory store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100; // max requests per window

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const key = `webhook_${ip}`;
  const current = rateLimitStore.get(key);

  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (current.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  current.count++;
  return true;
}

function validatePagarmeSignature(body: string, signature: string | null): boolean {
  if (!PAGARME_WEBHOOK_SECRET || !signature) {
    console.warn('Pagar.me webhook signature validation skipped - secret or signature missing');
    return true; // Allow for development, but log warning
  }

  try {
    const expectedSignature = crypto
      .createHmac('sha256', PAGARME_WEBHOOK_SECRET)
      .update(body, 'utf8')
      .digest('hex');

    const providedSignature = signature.replace('sha256=', '');
    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error validating Pagar.me signature:', error);
    return false;
  }
}


export async function POST(request: Request) {
  try {
    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';

    if (!checkRateLimit(clientIP)) {
      console.warn(`Rate limit exceeded for IP: ${clientIP}`);
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    // Get raw body for signature validation
    const rawBody = await request.text();
    const body = JSON.parse(rawBody);

    console.log('Pagar.me Webhook Received:', JSON.stringify(body, null, 2));

    // Signature validation
    const signature = request.headers.get('x-hub-signature-256') ||
      request.headers.get('x-hub-signature');

    if (!validatePagarmeSignature(rawBody, signature)) {
      console.error('Invalid Pagar.me webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const { type, data } = body;

    // Handle charge creation (pending status)
    if (type === 'charge.created' && data.status === 'pending') {
      const charge = data as PagarmeCharge;
      const chargeId = charge.id;
      const orderCode = charge.order?.code;

      if (!orderCode || !chargeId) {
        console.error('Webhook Error: Order code or charge ID not found in charge object.');
        return NextResponse.json({ error: 'Order code or charge ID missing' }, { status: 400 });
      }

      // Check if purchase was already created (idempotency)
      const existingPurchase = await getPurchaseByTransactionId(chargeId, 'pagarme');
      if (existingPurchase) {
        console.log(`Purchase ${chargeId} already exists, skipping creation.`);
        return NextResponse.json({ received: true, message: 'Already exists' }, { status: 200 });
      }

      // Get userId and packageId from metadata
      const metadata = charge.order?.metadata;
      const userId = metadata?.userId;
      const packageId = metadata?.packageId;

      if (!userId || !packageId) {
        console.error(`Webhook Error: Missing metadata in order: ${orderCode}`);
        return NextResponse.json({ error: 'Missing order metadata' }, { status: 400 });
      }

      const pkg = packages.find((p) => p.id === packageId);
      if (!pkg) {
        console.error(`Webhook Error: Package not found for id: ${packageId}`);
        return NextResponse.json({ error: 'Package not found' }, { status: 404 });
      }

      // Extract PIX data from last_transaction
      const pixTransaction = charge.last_transaction;
      const qrCodeUrl = pixTransaction?.qr_code_url;
      const qrCode = pixTransaction?.qr_code;
      const expiresAt = pixTransaction?.expires_at ? new Date(pixTransaction.expires_at) : undefined;

      // Create purchase record with pending status
      await createPurchase({
        userId,
        orderCode,
        packageId,
        packageName: pkg.name,
        amount: charge.amount,
        coins: pkg.coins,
        status: 'pending',
        paymentMethod: charge.payment_method as 'pix' | 'credit_card',
        gateway: 'pagarme',
        gatewayTransactionId: chargeId,
        gatewayOrderId: charge.order?.id,
        metadata: metadata || {},
        qrCodeUrl,
        qrCode,
        expiresAt,
        createdAt: new Date(charge.created_at),
        updatedAt: new Date(charge.updated_at),
      });

      console.log(`Successfully created pending purchase for user ${userId} with charge ${chargeId}.`);
      return NextResponse.json({ received: true }, { status: 200 });
    }

    // Handle charge payment (paid status)
    if (type === 'charge.paid' && data.status === 'paid') {
      const charge = data as PagarmeCharge;
      const transaction = charge.last_transaction;
      const chargeId = charge.id;
      const orderCode = charge.order?.code;

      if (!orderCode || !chargeId) {
        console.error('Webhook Error: Order code or charge ID not found in charge object.');
        return NextResponse.json({ error: 'Order code or charge ID missing' }, { status: 400 });
      }

      // Check if payment was already processed (idempotency)
      const existingPurchase = await getPurchaseByTransactionId(chargeId, 'pagarme');
      if (existingPurchase) {
        console.log(`Payment ${chargeId} already processed, skipping.`);
        return NextResponse.json({ received: true, message: 'Already processed' }, { status: 200 });
      }

      // Get userId and packageId from metadata
      const metadata = charge.order?.metadata;
      const userId = metadata?.userId;
      const packageId = metadata?.packageId;

      if (!userId || !packageId) {
        console.error(`Webhook Error: Missing metadata in order: ${orderCode}`);
        return NextResponse.json({ error: 'Missing order metadata' }, { status: 400 });
      }

      const pkg = packages.find((p) => p.id === packageId);
      if (!pkg) {
        console.error(`Webhook Error: Package not found for id: ${packageId}`);
        return NextResponse.json({ error: 'Package not found' }, { status: 404 });
      }

      // Create purchase record
      await createPurchase({
        userId,
        orderCode,
        packageId,
        packageName: pkg.name,
        amount: charge.amount,
        coins: pkg.coins,
        status: 'paid',
        paymentMethod: charge.payment_method as 'pix' | 'credit_card',
        gateway: 'pagarme',
        gatewayTransactionId: chargeId,
        gatewayOrderId: charge.order?.id,
        metadata: metadata || {},
        createdAt: new Date(charge.created_at),
        updatedAt: new Date(charge.updated_at),
        paidAt: charge.paid_at ? new Date(charge.paid_at) : new Date(),
        qrCodeUrl: transaction?.qr_code_url,
        qrCode: transaction?.qr_code,
        expiresAt: transaction?.expires_at ? new Date(transaction.expires_at) : undefined,
      });

      // Process payment
      await addCoins(userId, pkg.coins);

      console.log(`Successfully added ${pkg.coins} coins to user ${userId} for charge ${chargeId}.`);
    }

    return NextResponse.json({ received: true }, { status: 200 });

  } catch (error) {
    console.error('Webhook processing error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: 'Internal Server Error', details: errorMessage }, { status: 500 });
  }
}
