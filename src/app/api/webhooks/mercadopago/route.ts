// src/app/api/webhooks/mercadopago/route.ts
import { NextResponse } from 'next/server';
import { MercadoPagoConfig, Payment } from 'mercadopago';
import { addCoins } from '@/services/walletService';
import { packages } from '@/services/packages';
import { firestore } from '@/lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { createPurchase, getPurchaseByTransactionId } from '@/services/purchaseService';
import { notifyPaymentStatus } from '@/app/api/payment-events/route';
import crypto from 'crypto';

// Rate limiting - simple in-memory store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100; // max requests per window

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const key = `webhook_mp_${ip}`;
  const current = rateLimitStore.get(key);

  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (current.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  current.count++;
  return true;
}

function validateMercadoPagoSignature(
  xSignature: string | null,
  xRequestId: string | null,
  dataId: string
): boolean {
  const webhookSecret = process.env.MERCADOPAGO_WEBHOOK_SECRET;

  if (!webhookSecret || !xSignature || !xRequestId) {
    console.warn('Mercado Pago webhook signature validation skipped - secret or headers missing');
    return true; // Allow for development, but log warning
  }

  try {
    // Mercado Pago signature format: ts={timestamp},v1={signature}
    const parts = xSignature.split(',');
    const tsMatch = parts.find(part => part.startsWith('ts='));
    const v1Match = parts.find(part => part.startsWith('v1='));

    if (!tsMatch || !v1Match) {
      console.error('Invalid Mercado Pago signature format');
      return false;
    }

    const timestamp = tsMatch.split('=')[1];
    const signature = v1Match.split('=')[1];

    // Create the signed string
    const signedString = `id:${dataId};request-id:${xRequestId};ts:${timestamp};`;

    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(signedString, 'utf8')
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(signature, 'hex')
    );
  } catch (error) {
    console.error('Error validating Mercado Pago signature:', error);
    return false;
  }
}



export async function POST(request: Request) {
  try {
    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';

    if (!checkRateLimit(clientIP)) {
      console.warn(`Rate limit exceeded for IP: ${clientIP}`);
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    const body = await request.json();
    console.log('Mercado Pago Webhook Received:', JSON.stringify(body, null, 2));

    // Signature validation
    const xSignature = request.headers.get('x-signature');
    const xRequestId = request.headers.get('x-request-id');

    if (body.data?.id && !validateMercadoPagoSignature(
      xSignature,
      xRequestId,
      body.data.id.toString()
    )) {
      console.error('Invalid Mercado Pago webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const accessToken = process.env.MERCADOPAGO_ACCESS_TOKEN;
    if (!accessToken) {
      console.error('Mercado Pago Access Token is not configured.');
      return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }

    if (body.type === 'payment') {
      const paymentId = body.data.id.toString();

      // Check if payment was already processed (idempotency)
      const existingPurchase = await getPurchaseByTransactionId(paymentId, 'mercadopago');
      if (existingPurchase) {
        console.log(`Payment ${paymentId} already processed, skipping.`);
        return NextResponse.json({ received: true, message: 'Already processed' }, { status: 200 });
      }

      const paymentClient = new Payment(new MercadoPagoConfig({ accessToken }));
      const payment = await paymentClient.get({ id: body.data.id });

      if (payment && payment.status === 'approved' && payment.external_reference) {
        const orderCode = payment.external_reference;

        // Get userId and packageId from metadata
        const userId = payment.metadata?.userId;
        const packageId = payment.metadata?.packageId;

        if (!userId || !packageId) {
          console.error(`Webhook Error: Missing metadata in payment: ${orderCode}`);
          return NextResponse.json({ error: 'Missing payment metadata' }, { status: 400 });
        }

        const pkg = packages.find((p) => p.id === packageId);
        if (!pkg) {
          console.error(`Webhook Error: Package not found for id: ${packageId}`);
          return NextResponse.json({ error: 'Package not found' }, { status: 404 });
        }

        // Create purchase record
        await createPurchase({
          userId,
          orderCode,
          packageId,
          packageName: pkg.name,
          amount: payment.transaction_amount,
          coins: pkg.coins,
          status: 'paid',
          paymentMethod: payment.payment_method_id === 'pix' ? 'pix' : 'credit_card',
          gateway: 'mercadopago',
          gatewayTransactionId: paymentId,
          metadata: payment.metadata || {},
          createdAt: new Date(payment.date_created || new Date()),
          updatedAt: new Date(payment.date_last_updated || new Date()),
          paidAt: payment.date_approved ? new Date(payment.date_approved) : new Date(),
        });

        // Process payment
        await addCoins(userId, pkg.coins);

        // Notify user about successful payment
        notifyPaymentStatus(userId, {
          type: 'payment_approved',
          orderCode,
          amount: Math.round((payment.transaction_amount || 0) * 100), // Convert to cents
          coins: pkg.coins,
        });

        console.log(`Successfully added ${pkg.coins} coins to user ${userId} via Mercado Pago for payment ${paymentId}.`);
      }
    }

    return NextResponse.json({ received: true }, { status: 200 });

  } catch (error) {
    console.error('Mercado Pago webhook processing error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: 'Internal Server Error', details: errorMessage }, { status: 500 });
  }
}
