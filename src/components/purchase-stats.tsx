import * as React from 'react';
import {
  Card,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  ShoppingCart, 
  CheckCircle, 
  DollarSign, 
  Coins,
  TrendingUp,
  Percent,
} from 'lucide-react';
import { formatCurrency, formatNumber, calculateSuccessRate } from '@/lib/purchase-utils';

export interface PurchaseStatsData {
  totalPurchases: number;
  totalSpent: number; // in cents
  totalCoins: number;
  successfulPurchases: number;
}

interface PurchaseStatsProps {
  stats: PurchaseStatsData;
  loading?: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

function StatCard({ 
  title, 
  value, 
  icon: Icon, 
  description, 
  trend, 
  className = "" 
}: StatCardProps) {
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
        {trend && (
          <div className={`flex items-center text-xs mt-1 ${
            trend.isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            <TrendingUp className={`h-3 w-3 mr-1 ${
              trend.isPositive ? '' : 'rotate-180'
            }`} />
            {trend.value}% em relação ao mês anterior
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function StatCardSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4 rounded" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-16 mb-1" />
        <Skeleton className="h-3 w-32" />
      </CardContent>
    </Card>
  );
}

export function PurchaseStats({ stats, loading = false }: PurchaseStatsProps) {
  const successRate = calculateSuccessRate(stats.totalPurchases, stats.successfulPurchases);
  const averageOrderValue = stats.successfulPurchases > 0 
    ? stats.totalSpent / stats.successfulPurchases 
    : 0;

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <StatCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <StatCard
        title="Total de Compras"
        value={formatNumber(stats.totalPurchases)}
        icon={ShoppingCart}
        description="Todas as tentativas de compra"
      />
      
      <StatCard
        title="Compras Aprovadas"
        value={formatNumber(stats.successfulPurchases)}
        icon={CheckCircle}
        description={`${successRate}% de taxa de sucesso`}
        className="border-green-200 dark:border-green-800"
      />
      
      <StatCard
        title="Total Gasto"
        value={formatCurrency(stats.totalSpent)}
        icon={DollarSign}
        description={averageOrderValue > 0 ? `Média: ${formatCurrency(averageOrderValue)}` : undefined}
        className="border-blue-200 dark:border-blue-800"
      />
      
      <StatCard
        title="Moedas Adquiridas"
        value={formatNumber(stats.totalCoins)}
        icon={Coins}
        description="Total de moedas compradas"
        className="border-purple-200 dark:border-purple-800"
      />
    </div>
  );
}

interface PurchaseStatsCompactProps {
  stats: PurchaseStatsData;
  loading?: boolean;
}

export function PurchaseStatsCompact({ stats, loading = false }: PurchaseStatsCompactProps) {
  if (loading) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <Skeleton className="h-5 w-32" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="text-center">
                <Skeleton className="h-6 w-12 mx-auto mb-1" />
                <Skeleton className="h-3 w-16 mx-auto" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const successRate = calculateSuccessRate(stats.totalPurchases, stats.successfulPurchases);

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg">Resumo das Compras</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(stats.totalPurchases)}
            </div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-green-600">
              {formatNumber(stats.successfulPurchases)}
            </div>
            <div className="text-xs text-muted-foreground">Aprovadas</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(stats.totalSpent)}
            </div>
            <div className="text-xs text-muted-foreground">Gasto</div>
          </div>
          
          <div>
            <div className="text-2xl font-bold text-orange-600">
              {formatNumber(stats.totalCoins)}
            </div>
            <div className="text-xs text-muted-foreground">Moedas</div>
          </div>
        </div>
        
        {stats.totalPurchases > 0 && (
          <div className="mt-4 pt-4 border-t text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Percent className="h-4 w-4" />
              <span>Taxa de sucesso: {successRate}%</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
