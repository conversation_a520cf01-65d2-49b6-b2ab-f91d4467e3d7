import * as React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingBag, 
  Calendar, 
  CreditCard, 
  Coins,
  ExternalLink,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { Purchase } from '@/services/purchaseService';
import {
  formatCurrency,
  formatDate,
  formatNumber,
  getPurchaseDate,
  getStatusBadgeProps,
  getGatewayLabel,
  getPaymentMethodLabel,
} from '@/lib/purchase-utils';

interface PurchaseCardProps {
  purchase: Purchase;
  showDetails?: boolean;
  onViewDetails?: (purchase: Purchase) => void;
}

export function PurchaseCard({ 
  purchase, 
  showDetails = true, 
  onViewDetails 
}: PurchaseCardProps) {
  const createdAt = getPurchaseDate(purchase, 'created');
  const paidAt = purchase.paidAt ? getPurchaseDate(purchase, 'paid') : null;
  const statusBadge = getStatusBadgeProps(purchase.status);

  return (
    <Card className="w-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-sm font-medium">
              {purchase.packageName}
            </CardTitle>
          </div>
          <Badge 
            variant="outline" 
            className={statusBadge.className}
          >
            {statusBadge.label}
          </Badge>
        </div>
        <CardDescription className="text-xs">
          Pedido: {purchase.orderCode}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Coins className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Moedas:</span>
              <span className="font-medium">{formatNumber(purchase.coins)}</span>
            </div>
            <div className="flex items-center gap-2">
              <CreditCard className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Valor:</span>
              <span className="font-medium">{formatCurrency(purchase.amount)}</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Criado:</span>
              <span className="text-xs">{formatDate(createdAt)}</span>
            </div>
            {paidAt && (
              <div className="flex items-center gap-2">
                <Calendar className="h-3 w-3 text-green-600" />
                <span className="text-muted-foreground">Pago:</span>
                <span className="text-xs">{formatDate(paidAt)}</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="mt-3 pt-3 border-t flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>{getGatewayLabel(purchase.gateway)}</span>
            <span>{getPaymentMethodLabel(purchase.paymentMethod)}</span>
          </div>
          
          {showDetails && onViewDetails && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => onViewDetails(purchase)}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Detalhes
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface PurchaseCardSkeletonProps {
  count?: number;
}

export function PurchaseCardSkeleton({ count = 1 }: PurchaseCardSkeletonProps) {
  return (
    <>
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 bg-muted rounded animate-pulse" />
                <div className="h-5 w-32 bg-muted rounded animate-pulse" />
              </div>
              <div className="h-6 w-16 bg-muted rounded animate-pulse" />
            </div>
            <div className="h-4 w-48 bg-muted rounded animate-pulse" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="h-4 w-24 bg-muted rounded animate-pulse" />
                <div className="h-4 w-28 bg-muted rounded animate-pulse" />
              </div>
              <div className="space-y-2">
                <div className="h-4 w-32 bg-muted rounded animate-pulse" />
                <div className="h-4 w-32 bg-muted rounded animate-pulse" />
              </div>
            </div>
            <div className="mt-3 pt-3 border-t flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="h-3 w-16 bg-muted rounded animate-pulse" />
                <div className="h-3 w-20 bg-muted rounded animate-pulse" />
              </div>
              <div className="h-6 w-16 bg-muted rounded animate-pulse" />
            </div>
          </CardContent>
        </Card>
      ))}
    </>
  );
}
