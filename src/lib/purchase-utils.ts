import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import type { Purchase } from '@/services/purchaseService';

export const statusLabels = {
  pending: 'Pendente',
  paid: 'Pago',
  canceled: 'Cancelado',
  failed: 'Fal<PERSON>',
} as const;

export const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
  paid: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
  canceled: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800',
  failed: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
} as const;

export const gatewayLabels = {
  pagarme: 'Pagar.me',
  mercadopago: 'Mercado Pago',
} as const;

export const paymentMethodLabels = {
  pix: 'PIX',
  credit_card: 'Cartão de Crédito',
} as const;

/**
 * Formats a value in cents to Brazilian Real currency
 */
export function formatCurrency(cents: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(cents / 100);
}

/**
 * Formats a date to Brazilian format
 */
export function formatDate(date: Date | { toDate?: () => Date }, formatStr: string = 'dd/MM/yy HH:mm'): string {
  const dateObj = typeof date === 'object' && date.toDate ? date.toDate() : date as Date;
  return format(dateObj, formatStr, { locale: ptBR });
}

/**
 * Gets the appropriate date from a purchase (createdAt or paidAt)
 */
export function getPurchaseDate(purchase: Purchase, type: 'created' | 'paid' = 'created'): Date {
  if (type === 'paid' && purchase.paidAt) {
    return purchase.paidAt?.toDate?.() || new Date(purchase.paidAt as any);
  }
  return purchase.createdAt?.toDate?.() || new Date(purchase.createdAt as any);
}

/**
 * Formats a number with thousands separator
 */
export function formatNumber(num: number): string {
  return num.toLocaleString('pt-BR');
}

/**
 * Gets status badge props for a purchase
 */
export function getStatusBadgeProps(status: Purchase['status']) {
  return {
    label: statusLabels[status],
    className: statusColors[status],
  };
}

/**
 * Gets gateway display name
 */
export function getGatewayLabel(gateway: Purchase['gateway']): string {
  return gatewayLabels[gateway];
}

/**
 * Gets payment method display name
 */
export function getPaymentMethodLabel(paymentMethod: Purchase['paymentMethod']): string {
  return paymentMethodLabels[paymentMethod];
}

/**
 * Calculates purchase success rate
 */
export function calculateSuccessRate(totalPurchases: number, successfulPurchases: number): number {
  if (totalPurchases === 0) return 0;
  return Math.round((successfulPurchases / totalPurchases) * 100);
}

/**
 * Groups purchases by date
 */
export function groupPurchasesByDate(purchases: Purchase[]): Record<string, Purchase[]> {
  return purchases.reduce((groups, purchase) => {
    const date = formatDate(getPurchaseDate(purchase), 'dd/MM/yyyy');
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(purchase);
    return groups;
  }, {} as Record<string, Purchase[]>);
}

/**
 * Filters purchases by date range
 */
export function filterPurchasesByDateRange(
  purchases: Purchase[],
  startDate?: Date,
  endDate?: Date
): Purchase[] {
  return purchases.filter(purchase => {
    const purchaseDate = getPurchaseDate(purchase);

    if (startDate && purchaseDate < startDate) {
      return false;
    }

    if (endDate && purchaseDate > endDate) {
      return false;
    }

    return true;
  });
}

/**
 * Gets the most recent purchase
 */
export function getMostRecentPurchase(purchases: Purchase[]): Purchase | null {
  if (purchases.length === 0) return null;

  return purchases.reduce((latest, current) => {
    const latestDate = getPurchaseDate(latest);
    const currentDate = getPurchaseDate(current);
    return currentDate > latestDate ? current : latest;
  });
}

/**
 * Calculates total value for a list of purchases
 */
export function calculateTotalValue(purchases: Purchase[]): {
  totalAmount: number;
  totalCoins: number;
  paidAmount: number;
  paidCoins: number;
} {
  return purchases.reduce((totals, purchase) => {
    totals.totalAmount += purchase.amount;
    totals.totalCoins += purchase.coins;

    if (purchase.status === 'paid') {
      totals.paidAmount += purchase.amount;
      totals.paidCoins += purchase.coins;
    }

    return totals;
  }, {
    totalAmount: 0,
    totalCoins: 0,
    paidAmount: 0,
    paidCoins: 0,
  });
}
