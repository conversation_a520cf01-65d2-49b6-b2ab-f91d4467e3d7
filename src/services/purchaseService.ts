'use server';

import {
  collection,
  addDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot,
} from 'firebase/firestore';
import { firestore } from '@/lib/firebase';

export interface Purchase extends DocumentData {
  id: string;
  userId: string;
  orderCode: string;
  packageId: string;
  packageName: string;
  amount: number; // Amount in cents
  coins: number; // Number of coins purchased
  status: 'pending' | 'paid' | 'canceled' | 'failed';
  paymentMethod: 'pix' | 'credit_card';
  gateway: 'pagarme' | 'mercadopago';
  gatewayTransactionId: string; // chargeId for Pagarme, paymentId for MercadoPago
  gatewayOrderId?: string; // orderId from gateway
  metadata?: { [key: string]: string };
  createdAt: Timestamp;
  paidAt?: Timestamp;
  updatedAt: Timestamp;
}

export interface PurchaseCreateData {
  userId: string;
  orderCode: string;
  packageId: string;
  packageName: string;
  amount: number;
  coins: number;
  status: 'pending' | 'paid' | 'canceled' | 'failed';
  paymentMethod: 'pix' | 'credit_card';
  gateway: 'pagarme' | 'mercadopago';
  gatewayTransactionId: string;
  gatewayOrderId?: string;
  metadata?: { [key: string]: string };
  qrCodeUrl?: string;
  qrCode?: string;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  paidAt?: Date;
}

export interface PurchaseFilters {
  status?: 'pending' | 'paid' | 'canceled' | 'failed';
  gateway?: 'pagarme' | 'mercadopago';
  paymentMethod?: 'pix' | 'credit_card';
  startDate?: Date;
  endDate?: Date;
}

export interface PaginatedPurchases {
  purchases: Purchase[];
  hasMore: boolean;
  lastDoc?: QueryDocumentSnapshot<DocumentData>;
}

const purchasesCollection = 'purchases';
const FIREBASE_NOT_CONFIGURED_ERROR = "Firebase is not configured. Please check your environment variables.";

/**
 * Creates a new purchase record in Firestore
 */
export async function createPurchase(purchaseData: PurchaseCreateData): Promise<Purchase> {
  if (!firestore) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);

  const purchasesRef = collection(firestore, purchasesCollection);

  const dataToSave = {
    ...purchaseData,
    createdAt: Timestamp.fromDate(purchaseData.createdAt),
    updatedAt: Timestamp.fromDate(purchaseData.updatedAt),
    ...(purchaseData.paidAt && { paidAt: Timestamp.fromDate(purchaseData.paidAt) }),
  };

  const docRef = await addDoc(purchasesRef, dataToSave);

  const paidAt = purchaseData.paidAt ? Timestamp.fromDate(purchaseData.paidAt) : undefined;

  return {
    id: docRef.id,
    ...purchaseData,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
    paidAt,
  };
}

/**
 * Gets user purchases with optional filters and pagination
 */
export async function getUserPurchases(
  userId: string,
  filters: PurchaseFilters = {},
  pageSize: number = 20,
  lastDoc?: QueryDocumentSnapshot<DocumentData>
): Promise<PaginatedPurchases> {
  if (!firestore) {
    console.warn("Firebase not configured. Cannot get user purchases.");
    return { purchases: [], hasMore: false };
  }

  const purchasesRef = collection(firestore, purchasesCollection);

  // Build query with filters
  let q = query(
    purchasesRef,
    where('userId', '==', userId),
    orderBy('createdAt', 'desc'),
    limit(pageSize + 1) // Get one extra to check if there are more
  );

  // Apply filters
  if (filters.status) {
    q = query(q, where('status', '==', filters.status));
  }
  if (filters.gateway) {
    q = query(q, where('gateway', '==', filters.gateway));
  }
  if (filters.paymentMethod) {
    q = query(q, where('paymentMethod', '==', filters.paymentMethod));
  }

  // Add pagination
  if (lastDoc) {
    q = query(q, startAfter(lastDoc));
  }

  const querySnapshot = await getDocs(q);
  const docs = querySnapshot.docs;

  // Check if there are more results
  const hasMore = docs.length > pageSize;
  const purchases = docs.slice(0, pageSize); // Remove the extra doc

  const purchaseData: Purchase[] = purchases.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      userId: data.userId,
      orderCode: data.orderCode,
      packageId: data.packageId,
      packageName: data.packageName,
      amount: data.amount,
      coins: data.coins,
      status: data.status,
      paymentMethod: data.paymentMethod,
      gateway: data.gateway,
      gatewayTransactionId: data.gatewayTransactionId,
      gatewayOrderId: data.gatewayOrderId,
      metadata: data.metadata,
      createdAt: data.createdAt,
      paidAt: data.paidAt,
      updatedAt: data.updatedAt,
    };
  });

  return {
    purchases: purchaseData,
    hasMore,
    lastDoc: hasMore ? purchases[purchases.length - 1] : undefined,
  };
}

/**
 * Gets a purchase by gateway transaction ID to avoid duplicates
 */
export async function getPurchaseByTransactionId(
  gatewayTransactionId: string,
  gateway: 'pagarme' | 'mercadopago'
): Promise<Purchase | null> {
  if (!firestore) {
    console.warn("Firebase not configured. Cannot get purchase by transaction ID.");
    return null;
  }

  const purchasesRef = collection(firestore, purchasesCollection);
  const q = query(
    purchasesRef,
    where('gatewayTransactionId', '==', gatewayTransactionId),
    where('gateway', '==', gateway),
    limit(1)
  );

  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    return null;
  }

  const doc = querySnapshot.docs[0];
  const data = doc.data();

  return {
    id: doc.id,
    userId: data.userId,
    orderCode: data.orderCode,
    packageId: data.packageId,
    packageName: data.packageName,
    amount: data.amount,
    coins: data.coins,
    status: data.status,
    paymentMethod: data.paymentMethod,
    gateway: data.gateway,
    gatewayTransactionId: data.gatewayTransactionId,
    gatewayOrderId: data.gatewayOrderId,
    metadata: data.metadata,
    createdAt: data.createdAt,
    paidAt: data.paidAt,
    updatedAt: data.updatedAt,
  };
}

/**
 * Gets user purchase statistics
 */
export async function getUserPurchaseStats(userId: string): Promise<{
  totalPurchases: number;
  totalSpent: number; // in cents
  totalCoins: number;
  successfulPurchases: number;
}> {
  if (!firestore) {
    console.warn("Firebase not configured. Cannot get user purchase stats.");
    return { totalPurchases: 0, totalSpent: 0, totalCoins: 0, successfulPurchases: 0 };
  }

  const purchasesRef = collection(firestore, purchasesCollection);
  const q = query(purchasesRef, where('userId', '==', userId));

  const querySnapshot = await getDocs(q);

  let totalPurchases = 0;
  let totalSpent = 0;
  let totalCoins = 0;
  let successfulPurchases = 0;

  querySnapshot.forEach(doc => {
    const data = doc.data();
    totalPurchases++;

    if (data.status === 'paid') {
      successfulPurchases++;
      totalSpent += data.amount;
      totalCoins += data.coins;
    }
  });

  return {
    totalPurchases,
    totalSpent,
    totalCoins,
    successfulPurchases,
  };
}
